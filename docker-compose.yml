version: '3.8'

services:
  # Novel Parser Application
  novel-parser:
    build: .
    ports:
      - "5001:5001"
    volumes:
      - ./docs:/app/docs
      - ./data:/app/data
    environment:
      # Database Configuration
      # Options: sqlite, postgresql
      DATABASE_TYPE: ${DATABASE_TYPE:-sqlite}
      
      # SQLite Configuration (used when DATABASE_TYPE=sqlite)
      SQLITE_DB_PATH: ${SQLITE_DB_PATH:-data/novels.db}
      
      # PostgreSQL Configuration (used when DATABASE_TYPE=postgresql)
      POSTGRES_HOST: ${POSTGRES_HOST:-postgres}
      POSTGRES_PORT: ${POSTGRES_PORT:-5432}
      POSTGRES_DB: ${POSTGRES_DB:-novel_parser}
      POSTGRES_USER: ${POSTGRES_USER:-novel_parser}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      
      # Application Configuration
      API_HOST: ${API_HOST:-0.0.0.0}
      API_PORT: ${API_PORT:-5001}
      DOCS_DIR: ${DOCS_DIR:-docs}
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - novel-parser-network

  # PostgreSQL Database (only used when DATABASE_TYPE=postgresql)
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-novel_parser}
      POSTGRES_USER: ${POSTGRES_USER:-novel_parser}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - novel-parser-network
    profiles:
      - postgresql

volumes:
  postgres_data:

networks:
  novel-parser-network:
    driver: bridge
